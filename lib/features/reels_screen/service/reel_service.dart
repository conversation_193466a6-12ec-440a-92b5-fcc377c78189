import 'dart:convert';
import 'package:flowkar/core/utils/exports.dart';
import 'package:http/http.dart' as http;
import '../config/cache_config.dart';

// Global video manager to prevent audio overlaps
class GlobalVideoManager {
  static final GlobalVideoManager _instance = GlobalVideoManager._internal();
  factory GlobalVideoManager() => _instance;
  GlobalVideoManager._internal();

  String? _currentlyPlayingUrl;
  final Set<String> _activeUrls = {};

  void registerVideo(String url) {
    _activeUrls.add(url);
  }

  void unregisterVideo(String url) {
    _activeUrls.remove(url);
    if (_currentlyPlayingUrl == url) {
      _currentlyPlayingUrl = null;
    }
  }

  bool canPlayVideo(String url) {
    return _currentlyPlayingUrl == null || _currentlyPlayingUrl == url;
  }

  void setCurrentlyPlaying(String url) {
    _currentlyPlayingUrl = url;
  }

  void stopAllVideos() {
    _currentlyPlayingUrl = null;
    _activeUrls.clear();
  }

  bool isCurrentlyPlaying(String url) {
    return _currentlyPlayingUrl == url;
  }

  void cleanup() {
    _currentlyPlayingUrl = null;
    _activeUrls.clear();
  }

  void stopAllExcept(String url) {
    _currentlyPlayingUrl = url;
    _activeUrls.removeWhere((u) => u != url);
  }
}

// Model for API response
class ReelData extends ChangeNotifier {
  final int id;
  final String title;
  final String description;
  final String location;
  int _likes;
  final int dislikes;
  final int commentsCount;
  final List<String> files;
  final String latestComment;
  final ReelUser user;
  bool _isLiked;
  bool _isSaved;
  final String createdAt;

  ReelData({
    required this.id,
    required this.title,
    required this.description,
    required this.location,
    required int likes,
    required this.dislikes,
    required this.commentsCount,
    required this.files,
    required this.latestComment,
    required this.user,
    required bool isLiked,
    required bool isSaved,
    required this.createdAt,
  })  : _likes = likes,
        _isLiked = isLiked,
        _isSaved = isSaved;

  // Getters
  int get likes => _likes;
  bool get isLiked => _isLiked;
  bool get isSaved => _isSaved;

  // Methods to update state
  void toggleLike() {
    _isLiked = !_isLiked;
    _likes = _isLiked ? _likes + 1 : _likes - 1;
    notifyListeners();
  }

  void toggleSave() {
    _isSaved = !_isSaved;
    notifyListeners();
  }

  void setLikeState(bool liked) {
    if (_isLiked != liked) {
      _isLiked = liked;
      _likes = _isLiked ? _likes + 1 : _likes - 1;
      notifyListeners();
    }
  }

  void setSaveState(bool saved) {
    if (_isSaved != saved) {
      _isSaved = saved;
      notifyListeners();
    }
  }

  factory ReelData.fromJson(Map<String, dynamic> json) {
    return ReelData(
      id: json['id'] ?? 0,
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      location: json['location'] ?? '',
      likes: json['likes'] ?? 0,
      dislikes: json['dislikes'] ?? 0,
      commentsCount: json['comments_count'] ?? 0,
      files: List<String>.from(json['files'] ?? []),
      latestComment: json['latest_comment'] ?? '',
      user: ReelUser.fromJson(json['user'] ?? {}),
      isLiked: json['is_liked'] ?? false,
      isSaved: json['is_saved'] ?? false,
      createdAt: json['created_at'] ?? '',
    );
  }

  ReelData copyWith({
    int? id,
    String? title,
    String? description,
    String? location,
    int? likes,
    int? dislikes,
    int? commentsCount,
    List<String>? files,
    String? latestComment,
    ReelUser? user,
    bool? isLiked,
    bool? isSaved,
    String? createdAt,
  }) {
    return ReelData(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      location: location ?? this.location,
      likes: likes ?? this._likes,
      dislikes: dislikes ?? this.dislikes,
      commentsCount: commentsCount ?? this.commentsCount,
      files: files ?? this.files,
      latestComment: latestComment ?? this.latestComment,
      user: user ?? this.user,
      isLiked: isLiked ?? this._isLiked,
      isSaved: isSaved ?? this._isSaved,
      createdAt: createdAt ?? this.createdAt,
    );
  }
}

class ReelUser {
  final int userId;
  final String username;
  final String name;
  final String profileImage;

  ReelUser({
    required this.userId,
    required this.username,
    required this.name,
    required this.profileImage,
  });

  factory ReelUser.fromJson(Map<String, dynamic> json) {
    return ReelUser(
      userId: json['user_id'] ?? 0,
      username: json['username'] ?? '',
      name: json['name'] ?? '',
      profileImage: json['profile_image'] ?? '',
    );
  }

  ReelUser copyWith({
    int? userId,
    String? username,
    String? name,
    String? profileImage,
  }) {
    return ReelUser(
      userId: userId ?? this.userId,
      username: username ?? this.username,
      name: name ?? this.name,
      profileImage: profileImage ?? this.profileImage,
    );
  }
}

class ReelService extends ChangeNotifier {
  static const String _baseUrl = 'https://api.flowkar.com/api/get-reel-post/';
  static String _authToken = Prefobj.preferences?.get(Prefkeys.AUTHTOKEN);

  final List<ReelData> _reels = [];
  final Set<int> _loadedPages = {};
  bool _isLoading = false;
  bool _hasError = false;
  String _errorMessage = '';
  int _currentPage = 1;
  bool _hasMoreData = true;
  bool _isPaginationLoad = false; // Track if current load is pagination

  List<ReelData> get reels => _reels;
  bool get isLoading => _isLoading;
  bool get hasError => _hasError;
  String get errorMessage => _errorMessage;
  bool get hasMoreData => _hasMoreData;
  bool get isPaginationLoad => _isPaginationLoad;

  // Method to update auth token if needed
  void updateAuthToken(String newToken) {
    _authToken = newToken;
    // Clear cached data when token changes
    clearData();
  }

  // Get video URLs from reels
  List<String> getReelUrls() {
    return _reels.map((reel) => reel.files.isNotEmpty ? reel.files.first : '').toList();
  }

  // Fetch reels from API with pagination
  Future<void> fetchReels({int? page, bool isPagination = false}) async {
    final targetPage = page ?? _currentPage;

    // Prevent duplicate calls for the same page
    if (_loadedPages.contains(targetPage) || (_isLoading && !isPagination)) {
      return;
    }

    _isLoading = true;
    _isPaginationLoad = isPagination;
    _hasError = false;

    // Only notify listeners if it's not pagination to prevent unnecessary rebuilds
    if (!isPagination) {
      notifyListeners();
    }

    try {
      final response = await http.get(
        Uri.parse('$_baseUrl?page=$targetPage'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_authToken',
        },
      );

      if (response.statusCode == 200) {
        final data = json.decode(response.body);

        if (data['results'] != null && data['results']['data'] != null) {
          final List<dynamic> reelList = data['results']['data'];
          final List<ReelData> newReels = reelList.map((reelJson) => ReelData.fromJson(reelJson)).toList();

          // Add new reels to the list
          _reels.addAll(newReels);
          _loadedPages.add(targetPage);

          // Check if there's more data
          _hasMoreData = data['next'] != null;

          // Cache videos for new reels in background
          _cacheNewReels(newReels);

          Logger.lOG('Loaded ${newReels.length} reels from page $targetPage. Total reels: ${_reels.length}');
          Logger.lOG('Loaded page $targetPage. reels: ${response.body}');
        }
      } else {
        _hasError = true;
        _errorMessage = 'Failed to load reels: ${response.statusCode}';
        Logger.lOG('API Error: ${response.statusCode} - ${response.body}');
      }
    } catch (e) {
      _hasError = true;
      _errorMessage = 'Network error: $e';
      Logger.lOG('Network Error: $e');
    } finally {
      _isLoading = false;
      _isPaginationLoad = false;
      notifyListeners(); // Always notify after loading is complete
    }
  }

  // Cache new reels in background
  void _cacheNewReels(List<ReelData> newReels) {
    // Cache videos in background without blocking UI
    Future.microtask(() async {
      for (int i = 0; i < newReels.length; i++) {
        final reel = newReels[i];
        if (reel.files.isNotEmpty) {
          cacheVideo(reel.files.first);
        }
      }
    });
  }

  // Load next page
  Future<void> loadNextPage() async {
    if (!_isLoading && _hasMoreData) {
      _currentPage++;
      await fetchReels(page: _currentPage, isPagination: true);
    }
  }

  // Check if we should load more reels (when user reaches 3rd-last reel)
  void checkAndLoadMore(int currentIndex) {
    if (_reels.length - currentIndex <= 3 && !_isLoading && _hasMoreData) {
      Logger.lOG('Loading more reels as user reached index $currentIndex');
      loadNextPage();
    }
  }

  // Cache a single video
  Future<void> cacheVideo(String url) async {
    try {
      FileInfo? fileInfo = await kCacheManager.getFileFromCache(url);
      if (fileInfo == null) {
        Logger.lOG('Caching video: $url');
        await kCacheManager.downloadFile(url);
        Logger.lOG('Cached video: $url');
      }
    } catch (e) {
      Logger.lOG('Error caching video $url: $e');
    }
  }

  // Preload videos for better performance
  Future<void> preloadVideos(List<String> urls) async {
    for (final url in urls) {
      if (url.isNotEmpty) {
        cacheVideo(url);
      }
    }
  }

  // Get reels for a specific range (for optimization)
  List<ReelData> getReelsInRange(int startIndex, int endIndex) {
    if (startIndex < 0) startIndex = 0;
    if (endIndex > _reels.length) endIndex = _reels.length;
    return _reels.sublist(startIndex, endIndex);
  }

  // Cache all videos (legacy method for backward compatibility)
  Future<void> getVideosFromApI() async {
    await fetchReels();
  }

  // Retry loading on error
  Future<void> retry() async {
    _hasError = false;
    _errorMessage = '';
    await fetchReels();
  }

  // Clear all data (useful for refresh)
  void clearData() {
    _reels.clear();
    _loadedPages.clear();
    _currentPage = 1;
    _hasMoreData = true;
    _hasError = false;
    _errorMessage = '';
    _isPaginationLoad = false;
    notifyListeners();
  }

  // Refresh data (pull to refresh functionality)
  Future<void> refreshData() async {
    clearData();
    await fetchReels(page: 1);
  }

  // Legacy method for backward compatibility
  List<String> getReels() {
    return getReelUrls();
  }

  // Update individual reel data (useful for syncing with API responses)
  void updateReelData(int reelId, {bool? isLiked, bool? isSaved, int? likes}) {
    final reelIndex = _reels.indexWhere((reel) => reel.id == reelId);
    if (reelIndex != -1) {
      final reel = _reels[reelIndex];
      if (isLiked != null) {
        reel.setLikeState(isLiked);
      }
      if (isSaved != null) {
        reel.setSaveState(isSaved);
      }
      // Note: likes count is automatically managed by setLikeState
    }
  }

  // Get current reel count (useful for tracking changes)
  int get reelCount => _reels.length;
}
